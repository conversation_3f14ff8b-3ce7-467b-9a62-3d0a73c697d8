"use client";

import { motion } from 'framer-motion';
import { useTranslation } from '@/hooks/useTranslation';
import GSAPTextReveal from '@/components/GSAPTextReveal';
import { getPreset } from '@/components/GSAPTextReveal/presets';
import styles from './style.module.scss';
import Separator from '@/components/Separator';


export default function StatsCards() {
  const { t } = useTranslation('pages');

  // Fonction pour parser le texte et créer un lien sur le mot "Google"
  const parseTextWithGoogleLink = (text) => {
    const googleLinkUrl = "https://g.page/r/CdYePvCaFA87EAE/review";

    // Recherche le mot "Google" dans le texte (insensible à la casse)
    const googleRegex = /(Google)/gi;

    if (googleRegex.test(text)) {
      const parts = text.split(googleRegex);
      return parts.map((part, index) => {
        if (part.toLowerCase() === 'google') {
          return (
            <a
              key={index}
              href={googleLinkUrl}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.googleLink}
            >
              {part}
            </a>
          );
        }
        return part;
      });
    }

    return text;
  };

  const statsData = [
    {
      number: t('agency.stats.projects_number'),
      label: t('agency.stats.projects_completed'),
      color: '#19271B',
      id: 'projects'
    },
    {
      number: t('agency.stats.rating'),
      label: t('agency.stats.satisfied_clients'),
      color: '#FF413D',
      id: 'clients'
    },
    {
      number: t('agency.stats.countries_number'),
      label: t('agency.stats.multiple_countries'),
      color: '#C8CEC9',
      id: 'countries'
    }
  ];

  return (
    <section className={styles.statsSection}>
      <div className="container">
        <div className={styles.statsGrid}>
          {statsData.map((stat, index) => (
            <div key={stat.id} className={styles.statCard}>
              <GSAPTextReveal
                as="p"
                className="text-big"
                {...getPreset('lines', { delay: 2 + (index * 0.15), stagger: 0.1 })}
              >
                {parseTextWithGoogleLink(stat.label)}
              </GSAPTextReveal>
              <Separator
                animated={true}
              />
              <div
                className={`${styles.maskWrapper} ${styles.statCardBg}`}
                style={{
                  backgroundColor: stat.color,
                  clipPath: "polygon(18% 0%, 100% 0%, 100% 92%, 100% 100%, 0% 100%, 0% 23%)"
                }}
              >
                <div className={styles.cardContent}>
                  <motion.div
                    className={styles.number}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true, margin: "-50px" }}
                    transition={{ duration: 0.8, ease: 'easeInOut', delay: (index + 0.3) * 0.15 }}
                  >
                    {stat.number}
                  </motion.div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
