"use client";

import styles from './style.module.scss';
import { useInView, motion } from 'framer-motion';
import { useRef } from 'react';
import { slideUp, opacity } from './animation';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';

export default function ExpertiseAccordion({
    title = "Nos expertises",
    expertises = []
}) {
    const container = useRef(null);
    const isInView = useInView(container, { once: true });

    // Protection : s'assurer que expertises est un tableau
    const expertisesList = Array.isArray(expertises) ? expertises : [];

    return (
        <div ref={container} className={`${styles.expertiseAccordion} container medium`}>
            <div className={styles.body}>
                {/* Titre à gauche */}
                <div className={styles.titleSection}>
                    <h2>
                     Nos expertises
                    </h2>
                </div>

                {/* Accordéon à droite */}
                <motion.div
                    variants={opacity}
                    animate={isInView ? "open" : "closed"}
                    className={styles.accordionSection}
                >
                    <div className={styles.accordionWrapper}>
                        <Accordion>
                            {expertisesList.map((expertise, index) => (
                                <AccordionItem
                                    key={index}
                                    hideIcon={true}
                                    title={expertise.title}
                                    items={expertise.items || []}
                                />
                            ))}
                        </Accordion>
                    </div>
                </motion.div>
            </div>
        </div>
    );
}
